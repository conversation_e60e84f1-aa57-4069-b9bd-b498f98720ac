using System.Collections.Generic;
using Data.Global;
using Data.UI;
using Logic.Global;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

namespace Logic.UI
{
    public class MenuGenerator : Singleton<MenuGenerator>
    {
        public Menu ActiveMenu { get; set; }
        public RectTransform MiniGamePanelContent => miniGamePanel.GetUIComponent<RectTransform>(menuData.containerTemplate.contentId);
        public MenuUIData MenuUIData => menuData;

        [SerializeField] private MenuUIData menuData;
        [SerializeField] private GameObject miniGamePanelPrefab;
        [SerializeField] private GameObject fadePanel;
        [SerializeField] private RectTransform rootUITransform;
        [SerializeField] private Dictionary<EmptyVariable, UniversalUIElement> generatedMenus;
        [SerializeField] private UnityEvent onFinish;
        private EmptyVariable defaultMenuId;
        private UniversalUIElement miniGamePanel;
        private AddressablesHelper helper = new();

        private void Start()
        {
            Generate();
        }

        private void Update()
        {
            if (ActiveMenu != null)
            {
                if (UnityEngine.InputSystem.Keyboard.current.escapeKey.wasPressedThisFrame)
                {
                    if (ActiveMenu.previousMenuId != null && !miniGamePanel.isActiveAndEnabled)
                    {
                        RectTransformAnimation currentClose = generatedMenus[ActiveMenu.id].GetUIComponent<RectTransformAnimation>(menuData.containerTemplate.closeId);
                        RectTransformAnimation prevClose = generatedMenus[ActiveMenu.previousMenuId].GetUIComponent<RectTransformAnimation>(menuData.containerTemplate.closeId);

                        currentClose.Stop();
                        prevClose.Stop();

                        CloseMenu(ActiveMenu.id, ActiveMenu.previousMenuId);
                        ActiveMenu = FindMenu(ActiveMenu.previousMenuId);
                    }
                    else if (miniGamePanel.isActiveAndEnabled)
                    {
                        RectTransformAnimation mGCloseAnim = miniGamePanel.GetUIComponent<RectTransformAnimation>(menuData.containerTemplate.closeId);
                        mGCloseAnim.Play();
                    }
                }
            }
        }

        public RectTransform GetGeneratedMenuContent(EmptyVariable id)
        {
            if (generatedMenus.ContainsKey(id))
            {
                return generatedMenus[id].GetUIComponent<RectTransform>(menuData.containerTemplate.contentId);
            }

            return null;
        }

        public void OpenMiniGamePanel(List<string> supportedGames)
        {
            if (supportedGames.Contains("None"))
            {
                Debug.LogWarning("No supported games provided for MiniGamePanel.");
                return;
            }

            miniGamePanel.gameObject.SetActive(true);
            RectTransformAnimation openAnim = miniGamePanel.GetUIComponent<RectTransformAnimation>(menuData.containerTemplate.openId);
            openAnim.Play();

            for (int i = 0; i < MiniGamePanelContent.childCount; i++)
            {
                GameObject miniGameCard = MiniGamePanelContent.GetChild(i).gameObject;
                if (supportedGames.Contains(miniGameCard.name))
                {
                    miniGameCard.SetActive(true);
                }
                else
                {
                    miniGameCard.SetActive(false);
                }
            }
            miniGamePanel.transform.SetAsLastSibling();
        }

        private void Generate()
        {
            Application.targetFrameRate = 240;
            generatedMenus = new Dictionary<EmptyVariable, UniversalUIElement>();
            defaultMenuId = menuData.menus[0].id;

            foreach (var menu in menuData.menus)
            {
                bool isDefaultMenu = menu.previousMenuId == null && menu.id == defaultMenuId;
                GameObject prefab = isDefaultMenu ? menuData.containerTemplate.mainPrefab : menuData.containerTemplate.prefab;
                UniversalUIElement menuElement = Instantiate(prefab, rootUITransform).GetComponent<UniversalUIElement>();
                RectTransform content = menuElement.GetUIComponent<RectTransform>(menuData.containerTemplate.contentId);
                generatedMenus.Add(menu.id, menuElement);
                menuElement.gameObject.SetActive(false);

                // if (isDefaultMenu)
                // {
                //     Instantiate(fadePanel, rootUITransform);
                // }

                if (menu.previousMenuId != null)
                {
                    // RectTransform root = menuElement.GetUIComponent<RectTransform>(menuData.containerTemplate.rootId);
                    // UniversalUIElement backButtonElement = Instantiate(menuData.backButtonTemplate.prefab, root).GetComponent<UniversalUIElement>();
                    // Button backButton = backButtonElement.GetUIComponent<Button>(menuData.backButtonTemplate.buttonId);

                    // backButton.onClick.AddListener(() => CloseMenu(menu.id, menu.previousMenuId));
                }

                foreach (var card in menu.cardData)
                {
                    UniversalUIElement cardGO = Instantiate(menuData.cardTemplate.prefab, content).GetComponent<UniversalUIElement>();

                    Image icon = cardGO.GetUIComponent<Image>(menuData.cardTemplate.iconId);
                    icon.sprite = card.icon;

                    // LocalizeStringEvent localizeTitle = cardGO.GetUIComponent<LocalizeStringEvent>(menuData.cardTemplate.titleId);
                    // localizeTitle.StringReference = card.title;

                    Button button = cardGO.GetUIComponent<Button>(menuData.cardTemplate.buttonId);
                    button.onClick.AddListener(() => OpenMenu(menu.id, card.targetMenuId));

                    Menu targetMenu = FindMenu(card.targetMenuId);
                    button.onClick.AddListener(delegate { ActiveMenu = targetMenu; });
                }
            }

            Instantiate(fadePanel, rootUITransform);

            miniGamePanel = Instantiate(miniGamePanelPrefab, rootUITransform).GetComponent<UniversalUIElement>();
            miniGamePanel.gameObject.SetActive(false);
            miniGamePanel.gameObject.name = "MiniGamePanel";
            RectTransform content1 = miniGamePanel.GetUIComponent<RectTransform>(menuData.containerTemplate.contentId);
            foreach (var card in menuData.miniGameUIData)
            {
                UniversalUIElement cardGO = Instantiate(menuData.cardTemplate.prefab, content1).GetComponent<UniversalUIElement>();

                cardGO.gameObject.name = card.miniGameName.ToString();

                Image icon = cardGO.GetUIComponent<Image>(menuData.cardTemplate.iconId);
                icon.sprite = card.icon;

                // LocalizeStringEvent localizeTitle = cardGO.GetUIComponent<LocalizeStringEvent>(menuData.cardTemplate.titleId);
                // localizeTitle.StringReference = card.title;

                Button button = cardGO.GetUIComponent<Button>(menuData.cardTemplate.buttonId);
                button.onClick.AddListener(async () => await helper.LoadSceneAsync(card.sceneRef, LoadSceneMode.Single, true));
            }

            RectTransform miniGameRoot = miniGamePanel.GetUIComponent<RectTransform>(menuData.containerTemplate.rootId);
            // UniversalUIElement mGBackButtonElement = Instantiate(menuData.backButtonTemplate.prefab, miniGameRoot).GetComponent<UniversalUIElement>();
            // Button mGBackButton = mGBackButtonElement.GetUIComponent<Button>(menuData.backButtonTemplate.buttonId);

            // mGBackButton.onClick.AddListener(delegate
            // {
            //     RectTransformAnimation mGCloseAnim = miniGamePanel.GetUIComponent<RectTransformAnimation>(menuData.containerTemplate.closeId);
            //     mGCloseAnim.Play();
            // });


            generatedMenus[defaultMenuId].gameObject.SetActive(true);

            onFinish.Invoke();
        }

        private Menu FindMenu(EmptyVariable id)
        {
            foreach (var menu in menuData.menus)
            {
                if (menu.id == id)
                {
                    return menu;
                }
            }

            return null;
        }

        private void OpenMenu(EmptyVariable sourceMenuId, EmptyVariable targetMenuId)
        {
            ToggleMenu(sourceMenuId, targetMenuId, true, true);
            PlayAnimation(sourceMenuId, targetMenuId);
        }

        private void CloseMenu(EmptyVariable currentMenuId, EmptyVariable previousMenuId)
        {
            ToggleMenu(currentMenuId, previousMenuId, true, true);
            PlayAnimation(currentMenuId, previousMenuId);
        }

        private void PlayAnimation(EmptyVariable currentId, EmptyVariable targetMenuId)
        {
            UniversalUIElement current = generatedMenus[currentId];
            UniversalUIElement target = generatedMenus[targetMenuId];

            RectTransformAnimation currentAnimation = current.GetUIComponent<RectTransformAnimation>(menuData.containerTemplate.closeId);
            RectTransformAnimation targetAnimation = target.GetUIComponent<RectTransformAnimation>(menuData.containerTemplate.openId);

            currentAnimation.Play();
            targetAnimation.Play();
        }

        private void ToggleMenu(EmptyVariable sourceMenuId, EmptyVariable targetMenuId, bool sourceToggle, bool targetToggle)
        {
            GameObject sourceMenu = generatedMenus[sourceMenuId].gameObject;
            GameObject targetMenu = generatedMenus[targetMenuId].gameObject;

            if (sourceMenuId == null || targetMenuId == null)
            {
                Debug.LogWarning("One of the menus are null");
                return;
            }

            sourceMenu.gameObject.SetActive(sourceToggle);
            targetMenu.gameObject.SetActive(targetToggle);
        }

        // protected override void OnDestroy()
        // {
        //     helper.ReleaseAllAssets();
        // }
    }
}